<template>
  <div class="home-dashboard">
    <!-- 统计卡片 -->
    <div class="stat-cards">
   

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>商品数量</span>
            <el-icon class="icon"><Goods /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.productCount }}</div>
          <div class="label">在售商品</div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>用户数量</span>
            <el-icon class="icon"><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.userCount }}</div>
          <div class="label">注册用户</div>
        </div>
      </el-card>

  
    </div>



    <!-- 热门商品 -->
    <el-card class="hot-products" shadow="hover">
      <template #header>
        <div class="section-header">
          <span>热门商品</span>
          <el-button type="primary" link @click="goToProducts">查看全部</el-button>
        </div>
      </template>
      <div class="product-grid">
        <el-card 
          v-for="product in hotProducts" 
          :key="product.id" 
          class="product-card"
          shadow="hover"
        >
          <el-image 
            :src="product.image" 
            class="product-image"
            fit="cover"
          />
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">￥{{ product.price }}</div>
            <div class="product-stock">库存: {{ product.num }}</div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ShoppingCart, Goods, User, Calendar, Picture } from '@element-plus/icons-vue';
import orderService from '../services/orders';
import userService from '../services/users';
import productService from '../services/products';

const router = useRouter();
const stats = ref({
  orderCount: 0,
  productCount: 0,
  userCount: 0,
  todayOrders: 0
});

const recentOrders = ref([]);
const hotProducts = ref([]);

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取订单列表
    const ordersResponse = await orderService.getAllOrders();
    if (ordersResponse.data.code === 200) {
      const orders = ordersResponse.data.data || [];
      stats.value.orderCount = orders.length;
      
      // 计算今日订单
      const today = new Date().toISOString().split('T')[0];
      stats.value.todayOrders = orders.filter(order => 
        order.createTime.startsWith(today)
      ).length;

      // 获取最近5个订单
      recentOrders.value = orders
        .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
        .slice(0, 5);
    }

    // 获取商品列表
    const productsResponse = await productService.getAllProducts();
    if (productsResponse.data.code === 200) {
      const products = productsResponse.data.data || [];
      stats.value.productCount = products.length;
      
      // 获取前4个商品作为热门商品
      hotProducts.value = products.slice(0, 4);
    }

    // 获取用户列表
    const usersResponse = await userService.getAllUsers();
    if (usersResponse.data.code === 200) {
      const users = usersResponse.data.data || [];
      stats.value.userCount = users.length;
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 解析订单数据
const parseOrderData = (orderData) => {
  try {
    if (typeof orderData === 'string') {
      return JSON.parse(orderData);
    }
    return orderData;
  } catch (error) {
    console.error('解析订单数据失败:', error);
    return [];
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待支付',
    1: '已支付',
    2: '已发货',
    3: '已完成',
    4: '已取消'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态类型
const getStatusType = (status) => {
  const statusTypeMap = {
    0: 'warning',
    1: 'primary',
    2: 'success',
    3: 'info',
    4: 'danger'
  };
  return statusTypeMap[status] || 'info';
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 页面跳转
const goToOrders = () => {
  router.push('/orders');
};

const goToProducts = () => {
  router.push('/products');
};

// 页面加载时获取数据
onMounted(() => {
  fetchStats();
});
</script>

<style lang="scss" scoped>
.home-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 24px;

    .stat-card {
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      overflow: hidden;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        // border-radius: 5%;
        background: linear-gradient(135deg, #940b59 0%, #b48010 100%);
        color: white;

        .icon {
          font-size: 24px;
          opacity: 0.8;
        }
      }

      .card-content {
        padding: 20px;
        text-align: center;

        .number {
          font-size: 36px;
          font-weight: bold;
          color: #2c3e50;
          margin-bottom: 8px;
          font-family: 'Arial', sans-serif;
        }

        .label {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .recent-orders {
    margin-bottom: 24px;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      font-size: 18px;
      font-weight: 500;
      color: #2c3e50;
    }

    .order-items {
      .order-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .product-img {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          margin-right: 12px;
          object-fit: cover;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }

        .item-details {
          flex: 1;

          .item-name {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .item-price {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }

  .hot-products {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      font-size: 18px;
      font-weight: 500;
      color: #2c3e50;
    }

    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      padding: 20px;

      .product-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);

          .product-image {
            transform: scale(1.05);
          }
        }

        .product-image {
          width: 100%;
          height: 200px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .product-info {
          padding: 16px;

          .product-name {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .product-price {
            color: #e74c3c;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .product-stock {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 添加动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card,
.recent-orders,
.hot-products {
  animation: fadeIn 0.5s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.recent-orders { animation-delay: 0.5s; }
.hot-products { animation-delay: 0.6s; }

// 响应式调整
@media (max-width: 768px) {
  .home-dashboard {
    padding: 10px;

    .stat-cards {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .product-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
  }
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>