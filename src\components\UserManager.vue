<template>
  <div class="user-manager">
    <header class="header">
      <h1>用户管理</h1>
      <div class="actions">
        <el-button type="primary" @click="showAddUserDialog">
          添加用户
        </el-button>
      </div>
    </header>

    <!-- Loading 状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
      加载中...
    </div>

    <!-- 用户列表 -->
    <el-table
      v-else
      :data="users"
      style="width: 100%"
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: 'bold'
      }"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column label="头像" width="100">
        <template #default="{ row }">
          <el-avatar :size="40" :src="row.avatar">
            <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
          </el-avatar>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" />
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <button
            class="action-btn edit-btn"
            @click="showEditUserDialog(row)"
          >
            编辑
          </button>
          <button
            class="action-btn delete-btn"
            @click="confirmDeleteUser(row)"
          >
            删除
          </button>
          <button
            class="action-btn account-btn"
            @click="showAccountDialog(row)"
          >
            账户
          </button>
          <button
            class="action-btn create-account-btn"
            @click="createAccount(row)"
          >
            开户
          </button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="userDialog.visible"
      :title="userDialog.isEdit ? '编辑用户' : '添加用户'"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleDialogClose"
      destroy-on-close
    >
      <el-form
        ref="userFormRef"
        :model="userDialog.form"
        :rules="userFormRules"
        label-width="100px"
        class="user-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userDialog.form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userDialog.form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userDialog.form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="userDialog.form.avatar" placeholder="请输入头像URL" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="userDialog.form.address" placeholder="请输入地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeUserDialog">取消</el-button>
          <el-button
            type="primary"
            @click="handleUserSubmit"
            :loading="userDialog.loading"
          >
            {{ userDialog.isEdit ? '保存' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import userService from '../services/users';
import Swal from 'sweetalert2';

const loading = ref(false);
const users = ref([]);

// 表单校验规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 40, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' }
  ]
};

// 用户对话框数据
const userDialog = ref({
  visible: false,
  isEdit: false,
  loading: false,
  form: {
    id: '',
    username: '',
    password: '',
    name: '',
    avatar: '',
    address: ''
  }
});

// 账户管理对话框数据
const accountDialog = ref({
  visible: false,
  loading: false,
  userId: null,
  accountInfo: null
});

// 创建账户对话框数据
const createAccountDialog = ref({
  visible: false,
  loading: false,
  form: {
    money: 0
  }
});

// 充值对话框数据
const rechargeDialog = ref({
  visible: false,
  loading: false,
  form: {
    money: 0
  }
});

const userFormRef = ref(null);
const createAccountFormRef = ref(null);
const rechargeFormRef = ref(null);

// 获取所有用户
const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await userService.getAllUsers();
    console.log('获取用户列表响应:', response);

    if (response.data.code === 200) {
      users.value = response.data.data;
      console.log('用户列表数据:', users.value);
    } else {
      ElMessage({
        type: 'error',
        message: response.data.msg || '获取用户列表失败',
        duration: 2000,
        showClose: true
      });
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage({
      type: 'error',
      message: '获取用户列表失败：' + (error.message || '未知错误'),
      duration: 2000,
      showClose: true
    });
  } finally {
    loading.value = false;
  }
};

// 显示添加用户对话框
const showAddUserDialog = () => {
  userDialog.value.isEdit = false;
  userDialog.value.visible = true;
};

// 显示编辑用户对话框
const showEditUserDialog = (user) => {
  userDialog.value.isEdit = true;
  userDialog.value.form = { ...user };
  userDialog.value.visible = true;
};

// 关闭用户对话框
const closeUserDialog = () => {
  userDialog.value.visible = false;
  userDialog.value.loading = false;
  userDialog.value.form = {
    id: '',
    username: '',
    password: '',
    name: '',
    avatar: '',
    address: ''
  };
  if (userFormRef.value) {
    userFormRef.value.resetFields();
  }
};

// 处理对话框关闭
const handleDialogClose = () => {
  closeUserDialog();
};

// 处理用户表单提交
const handleUserSubmit = async () => {
  if (!userFormRef.value) return;

  await userFormRef.value.validate(async (valid) => {
    if (!valid) return;

    userDialog.value.loading = true;
    try {
      // 转换密码为字符串
      const userData = {
        ...userDialog.value.form,
        password: String(userDialog.value.form.password)
      };

      const response = userDialog.value.isEdit
        ? await userService.updateUser(userData)
        : await userService.addUser(userData);

      if (response.data.code === 200) {
        ElMessage({
          type: 'success',
          message: `${userDialog.value.isEdit ? '更新' : '添加'}用户成功`,
          duration: 2000,
          showClose: true
        });
        closeUserDialog();
        await fetchUsers(); // 刷新用户列表
      } else {
        ElMessage({
          type: 'error',
          message: response.data.msg || `${userDialog.value.isEdit ? '更新' : '添加'}失败`,
          duration: 2000,
          showClose: true
        });
      }
    } catch (error) {
      console.error(`${userDialog.value.isEdit ? '更新' : '添加'}用户失败:`, error);
      ElMessage({
        type: 'error',
        message: `${userDialog.value.isEdit ? '更新' : '添加'}失败：` + (error.message || '未知错误'),
        duration: 2000,
        showClose: true
      });
    } finally {
      userDialog.value.loading = false;
    }
  });
};

// 显示账户管理对话框
const showAccountDialog = async (user) => {
  try {
    // 先查询账户信息
    const response = await userService.getAccountInfo(user.id);
    
    if (response.data.code === 200) {
      // 账户存在，显示账户信息
      const result = await Swal.fire({
        title: '账户管理',
        html: `
          <div class="account-info">
            <p>账户余额：<span class="money">¥${response.data.data.money}</span></p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: '充值',
        cancelButtonText: '关闭',
        customClass: {
          container: 'account-swal'
        }
      });

      if (result.isConfirmed) {
        // 点击充值按钮
        showRechargeDialog(user.id, response.data.data.money);
      }
    } else if (response.data.code === 404) {
      // 账户不存在，自动创建账户
      const createResult = await Swal.fire({
        title: '创建账户',
        text: '该用户尚未开户，是否立即开户？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '开户',
        cancelButtonText: '取消'
      });

      if (createResult.isConfirmed) {
        try {
          const createResponse = await userService.createAccount(user.id, 0);
          if (createResponse.data === true) {
            Swal.fire('成功', '开户成功', 'success');
            // 重新查询账户信息
            showAccountDialog(user);
          } else {
            Swal.fire('错误', '开户失败', 'error');
          }
        } catch (error) {
          console.error('开户失败:', error);
          Swal.fire('错误', '开户失败：' + (error.message || '未知错误'), 'error');
        }
      }
    }
  } catch (error) {
    console.error('获取账户信息失败:', error);
    Swal.fire('错误', '获取账户信息失败', 'error');
  }
};

// 显示充值对话框
const showRechargeDialog = async (userId, currentBalance) => {
  const { value: money } = await Swal.fire({
    title: '账户充值',
    html: `
      <div class="recharge-info">
        <p>当前余额：<span class="money">¥${currentBalance}</span></p>
      </div>
    `,
    input: 'number',
    inputLabel: '请输入充值金额',
    inputValue: 0,
    inputAttributes: {
      min: 0,
      step: 10
    },
    showCancelButton: true,
    confirmButtonText: '充值',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '请输入金额';
      }
      if (value < 0) {
        return '金额不能小于0';
      }
    },
    customClass: {
      container: 'recharge-swal'
    }
  });

  if (money !== undefined) {
    try {
      const response = await userService.rechargeMoney(userId, money);
      if (response.data.code === 200) {
        Swal.fire({
          title: '成功',
          text: '充值成功',
          icon: 'success',
          timer: 1500,
          showConfirmButton: false
        });
        // 重新查询账户信息
        const user = users.value.find(u => u.id === userId);
        if (user) {
          showAccountDialog(user);
        }
      } else {
        Swal.fire('错误', response.data.msg || '充值失败', 'error');
      }
    } catch (error) {
      console.error('充值失败:', error);
      Swal.fire('错误', '充值失败：' + (error.message || '未知错误'), 'error');
    }
  }
};

// 确认删除用户
const confirmDeleteUser = async (user) => {
  const result = await Swal.fire({
    title: '删除确认',
    text: `确定要删除用户 "${user.username}" 吗？`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  });

  if (result.isConfirmed) {
    try {
      const response = await userService.deleteUser(user.id);
      
      if (response.data.code === 200) {
        Swal.fire('成功', '删除用户成功', 'success');
        await fetchUsers();
      } else {
        Swal.fire('错误', response.data.msg || '删除失败', 'error');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      Swal.fire('错误', '删除失败：' + (error.message || '未知错误'), 'error');
    }
  }
};

// 创建账户
const createAccount = async (user) => {
  const result = await Swal.fire({
    title: '创建账户',
    text: '确定要为该用户创建账户吗？',
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: '开户',
    cancelButtonText: '取消'
  });

  if (result.isConfirmed) {
    try {
      const response = await userService.createAccount(user.id, 0);
      if (response.data === true) {
        Swal.fire('成功', '开户成功', 'success');
      } else {
        Swal.fire('错误', '开户失败', 'error');
      }
    } catch (error) {
      console.error('开户失败:', error);
      Swal.fire('错误', '开户失败：' + (error.message || '未知错误'), 'error');
    }
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchUsers();
});
</script>

<style lang="scss" scoped>
.user-manager {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      font-size: 24px;
      color: #303133;
      margin: 0;
    }
  }

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #909399;
    font-size: 14px;

    .loading-icon {
      margin-right: 8px;
      font-size: 20px;
      animation: rotating 2s linear infinite;
    }
  }
}

.user-form {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 8px;
  transition: all 0.3s ease;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.edit-btn {
    background-color: #409eff;
    color: white;

    &:hover {
      background-color: #66b1ff;
    }
  }

  &.delete-btn {
    background-color: #f56c6c;
    color: white;

    &:hover {
      background-color: #f78989;
    }
  }

  &.account-btn {
    background-color: #67c23a;
    color: white;

    &:hover {
      background-color: #85ce61;
    }
  }

  &.create-account-btn {
    background-color: #e6a23c;
    color: white;

    &:hover {
      background-color: #ebb563;
    }
  }
}

:deep(.account-swal) {
  .account-info {
    margin: 20px 0;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 8px;
    text-align: center;

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;

      .money {
        font-size: 24px;
        font-weight: bold;
        color: #67c23a;
      }
    }
  }
}

:deep(.recharge-swal) {
  .recharge-info {
    margin: 20px 0;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 8px;
    text-align: center;

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;

      .money {
        font-size: 24px;
        font-weight: bold;
        color: #67c23a;
      }
    }
  }
}
</style> 