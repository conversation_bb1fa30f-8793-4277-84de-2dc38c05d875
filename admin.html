<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胖乖生活 Token 管理系统</title>
    <!-- 引入必要的 CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.tailwindcss.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Tailwind 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a73e8',
                        success: '#34a853',
                        warning: '#fbbc05',
                        danger: '#ea4335',
                        neutral: '#f5f7fa',
                        'neutral-dark': '#e4e7eb',
                        'text-primary': '#333333',
                        'text-secondary': '#666666'
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            }
            .btn-hover {
                @apply transition-all duration-300 hover:shadow-lg;
            }
            .status-badge {
                @apply px-3 py-1 rounded-full text-sm font-medium;
            }
            .token-text {
                @apply font-mono bg-neutral px-2 py-1 rounded text-sm overflow-hidden text-ellipsis whitespace-nowrap;
            }
            .fade-in {
                animation: fadeIn 0.5s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        }
    </style>
</head>
<body class="bg-neutral font-inter text-text-primary min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- 顶部导航栏 -->
        <header class="mb-8 fade-in">
            <div class="flex justify-between items-center bg-white rounded-xl p-6 card-shadow">
                <div class="flex items-center space-x-4">
                    <div class="bg-primary text-white p-3 rounded-lg">
                        <i class="fa fa-key text-xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-primary">胖乖生活 Token 管理系统</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchUser" placeholder="搜索用户名..." 
                            class="pl-10 pr-4 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 transition-all">
                        <i class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <button id="refreshBtn" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center btn-hover">
                        <i class="fa fa-refresh mr-2"></i> 刷新数据
                    </button>
                </div>
            </div>
        </header>
        
        <!-- API配置面板 -->
        <div class="bg-white rounded-xl p-6 card-shadow mb-6 fade-in">
            <h2 class="text-xl font-bold mb-4 flex items-center">
                <i class="fa fa-cog text-primary mr-2"></i> API 配置
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">获取Token列表</label>
                    <input type="text" id="apiTokenList" value="http://localhost/token/all" 
                        class="w-full px-3 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">获取用户信息</label>
                    <input type="text" id="apiUserInfo" value="https://userapi.qiekj.com/user/info" 
                        class="w-full px-3 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">获取当前密钥</label>
                    <input type="text" id="apiCurrentKey" value="http://localhost/token/getKeyForIdOne" 
                        class="w-full px-3 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button id="saveApiConfigBtn" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center btn-hover">
                    <i class="fa fa-save mr-2"></i> 保存配置
                </button>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <main class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧信息面板 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 系统概览卡片 -->
                <div class="bg-white rounded-xl p-6 card-shadow fade-in">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-dashboard text-primary mr-2"></i> 系统概览
                    </h2>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-neutral p-4 rounded-lg">
                            <p class="text-sm text-text-secondary">总用户数</p>
                            <p class="text-2xl font-bold text-primary" id="totalUsers">0</p>
                        </div>
                        <div class="bg-neutral p-4 rounded-lg">
                            <p class="text-sm text-text-secondary">有效Token</p>
                            <p class="text-2xl font-bold text-success" id="activeTokens">0</p>
                        </div>
                        <div class="bg-neutral p-4 rounded-lg">
                            <p class="text-sm text-text-secondary">无效Token</p>
                            <p class="text-2xl font-bold text-danger" id="inactiveTokens">0</p>
                        </div>
                        <div class="bg-neutral p-4 rounded-lg">
                            <p class="text-sm text-text-secondary">最后更新</p>
                            <p class="text-lg font-medium" id="lastUpdated">--</p>
                        </div>
                    </div>
                </div>
                
                <!-- 当前密钥管理 -->
                <div class="bg-white rounded-xl p-6 card-shadow fade-in">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-key text-primary mr-2"></i> 当前密钥
                    </h2>
                    <div class="space-y-4">
                        <div class="relative">
                            <input type="text" id="currentKey" readonly 
                                class="w-full token-text bg-neutral-dark px-4 py-3 rounded-lg focus:outline-none">
                            <button id="copyKeyBtn" class="absolute right-2 top-1/2 -translate-y-1/2 bg-primary/10 hover:bg-primary/20 text-primary p-1.5 rounded transition-all">
                                <i class="fa fa-copy"></i>
                            </button>
                        </div>
                        <div class="flex space-x-2">
                            <button id="generateKeyBtn" class="flex-1 bg-warning hover:bg-warning/90 text-white px-4 py-2 rounded-lg flex items-center justify-center btn-hover">
                                <i class="fa fa-random mr-2"></i> 生成新密钥
                            </button>
                            <button id="updateKeyBtn" class="flex-1 bg-success hover:bg-success/90 text-white px-4 py-2 rounded-lg flex items-center justify-center btn-hover">
                                <i class="fa fa-save mr-2"></i> 更新密钥
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 使用帮助 -->
                <div class="bg-white rounded-xl p-6 card-shadow fade-in">
                    <h2 class="text-xl font-bold mb-4 flex items-center">
                        <i class="fa fa-question-circle text-primary mr-2"></i> 使用帮助
                    </h2>
                    <div class="space-y-3 text-sm text-text-secondary">
                        <p><i class="fa fa-info-circle text-primary mr-2"></i> 系统每5分钟自动刷新Token状态</p>
                        <p><i class="fa fa-exclamation-triangle text-warning mr-2"></i> Token有效期为30天，请及时更新</p>
                        <p><i class="fa fa-refresh text-primary mr-2"></i> 点击"刷新数据"可手动更新Token状态</p>
                        <p><i class="fa fa-key text-primary mr-2"></i> 生成新密钥后需点击"更新密钥"保存</p>
                    </div>
                </div>
            </div>
            
            <!-- 右侧表格区域 -->
            <div class="lg:col-span-2 bg-white rounded-xl p-6 card-shadow fade-in">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold flex items-center">
                        <i class="fa fa-users text-primary mr-2"></i> 用户Token列表
                    </h2>
                    <div class="flex space-x-2">
                        <button id="addTokenBtn" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center btn-hover">
                            <i class="fa fa-plus mr-2"></i> 添加Token
                        </button>
                        <button id="batchUpdateBtn" class="bg-success hover:bg-success/90 text-white px-4 py-2 rounded-lg flex items-center btn-hover">
                            <i class="fa fa-refresh mr-2"></i> 批量更新
                        </button>
                    </div>
                </div>
                
                <!-- Token表格 -->
                <div class="overflow-x-auto">
                    <table id="tokenTable" class="w-full">
                        <thead>
                            <tr class="bg-neutral">
                                <th class="px-4 py-3 text-left text-sm font-semibold">ID</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold">用户名</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold">Token值</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold">创建时间</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold">状态</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <footer class="mt-8 text-center text-sm text-text-secondary fade-in">
            <p>© 2025 胖乖生活 Token 管理系统 | 版本 1.0.0</p>
        </footer>
    </div>
    
    <!-- 引入必要的 JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.4.0/dist/axios.min.js"></script>
    
    <script>
        // API配置
        const apiConfig = {
            tokenList: 'http://localhost/token/all',
            userInfo: 'https://userapi.qiekj.com/user/info',
            currentKey: 'http://localhost/token/getKeyForIdOne'
        };
        
        // 创建Axios实例
        const api = axios.create({
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        // 请求拦截器
        api.interceptors.request.use(config => {
            // 显示加载指示器
            Swal.showLoading();
            return config;
        }, error => {
            // 错误处理
            Swal.close();
            console.error('请求错误:', error);
            Swal.fire({
                title: '请求失败',
                text: error.message || '请求过程中发生错误',
                icon: 'error'
            });
            return Promise.reject(error);
        });
        
        // 响应拦截器
        api.interceptors.response.use(response => {
            // 隐藏加载指示器
            Swal.close();
            return response;
        }, error => {
            // 错误处理
            Swal.close();
            console.error('响应错误:', error);
            
            let errorMessage = '未知错误';
            if (error.response) {
                // 服务器返回错误状态码
                errorMessage = `服务器错误 ${error.response.status}: ${error.response.data.message || '服务器返回错误'}`;
            } else if (error.request) {
                // 请求已发送，但没有响应
                errorMessage = '没有收到服务器响应';
            } else {
                // 请求设置时出错
                errorMessage = error.message;
            }
            
            Swal.fire({
                title: '响应失败',
                text: errorMessage,
                icon: 'error'
            });
            
            return Promise.reject(error);
        });
        
        let tokenDataTable;
        let tokenData = [];
        
        // 初始化表格
        $(document).ready(function() {
            tokenDataTable = $('#tokenTable').DataTable({
                language: {
                    "sProcessing": "处理中...",
                    "sLengthMenu": "显示 _MENU_ 条",
                    "sZeroRecords": "没有找到相关数据",
                    "sInfo": "显示第 _START_ 至 _END_ 项，共 _TOTAL_ 项",
                    "sInfoEmpty": "显示第 0 至 0 项，共 0 项",
                    "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                    "sInfoPostFix": "",
                    "sSearch": "搜索:",
                    "sUrl": "",
                    "sEmptyTable": "暂无数据",
                    "sLoadingRecords": "载入中...",
                    "sInfoThousands": ",",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "上页",
                        "sNext": "下页",
                        "sLast": "末页"
                    }
                },
                columns: [
                    { data: 'id' },
                    { 
                        data: 'userName',
                        render: function(data, type, row) {
                            return `<div class="font-medium">${data}</div>`;
                        }
                    },
                    { 
                        data: 'token',
                        render: function(data) {
                            return `<div class="token-text" title="${data}">${data}</div>`;
                        }
                    },
                    { 
                        data: 'creatTime',
                        render: function(data) {
                            return new Date(data).toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                    },
                    {
                        data: 'status',
                        render: function(data, type, row) {
                            const days = calculateDaysDiff(row.createTime);
                            if (data === 'active') {
                                if (days >= 30) {
                                    return `<span class="status-badge bg-danger text-white">需要更新</span>`;
                                } else if (days >= 15) {
                                    return `<span class="status-badge bg-warning text-black">即将过期</span>`;
                                } else {
                                    return `<span class="status-badge bg-success text-white">正常</span>`;
                                }
                            } else {
                                return `<span class="status-badge bg-neutral-dark text-text-secondary">已失效</span>`;
                            }
                        }
                    },
                    {
                        data: null,
                        render: function(data, type, row) {
                            const days = calculateDaysDiff(row.createTime);
                            const updateBtnClass = days >= 30 ? 'bg-danger hover:bg-danger/90' : 'bg-warning hover:bg-warning/90';
                            const updateBtnText = days >= 30 ? '紧急更新' : '更新Token';
                            
                            return `
                                <div class="flex space-x-2">
                                    <button onclick="updateToken(${row.id})" class="px-3 py-1.5 rounded-lg ${updateBtnClass} text-white text-sm flex items-center btn-hover">
                                        <i class="fa fa-refresh mr-1"></i> ${updateBtnText}
                                    </button>
                                    <button onclick="deleteToken(${row.id})" class="px-3 py-1.5 rounded-lg bg-neutral-dark hover:bg-neutral-dark/80 text-text-secondary text-sm flex items-center btn-hover">
                                        <i class="fa fa-trash mr-1"></i> 删除
                                    </button>
                                </div>
                            `;
                        }
                    }
                ],
                createdRow: function(row, data, dataIndex) {
                    // 为行添加淡入动画
                    $(row).addClass('fade-in');
                    
                    // 根据状态添加行样式
                    const days = calculateDaysDiff(data.createTime);
                    if (data.status === 'active') {
                        if (days >= 30) {
                            $(row).addClass('bg-danger/5');
                        } else if (days >= 15) {
                            $(row).addClass('bg-warning/5');
                        }
                    }
                }
            });
            
            // 初始化界面
            initUI();
            
            // 加载数据
            loadTokenData();
            
            // 设置自动刷新
            setInterval(loadTokenData, 300000); // 5分钟刷新一次
            
            // 搜索框事件
            $('#searchUser').on('input', function() {
                tokenDataTable.search(this.value).draw();
            });
            
            // 按钮事件
            $('#refreshBtn').on('click', loadTokenData);
            $('#copyKeyBtn').on('click', copyCurrentKey);
            $('#generateKeyBtn').on('click', generateNewKey);
            $('#updateKeyBtn').on('click', updateKey);
            $('#addTokenBtn').on('click', addNewToken);
            $('#batchUpdateBtn').on('click', batchUpdateTokens);
            $('#saveApiConfigBtn').on('click', saveApiConfig);
        });
        
        // 初始化UI
        function initUI() {
            // 显示欢迎消息
            Swal.fire({
                title: '欢迎使用Token管理系统',
                text: '系统已准备就绪',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
            
            // 加载当前密钥
            fetchCurrentKey();
        }
        
        // 保存API配置
        function saveApiConfig() {
            apiConfig.tokenList = $('#apiTokenList').val();
            apiConfig.userInfo = $('#apiUserInfo').val();
            apiConfig.currentKey = $('#apiCurrentKey').val();
            
            Swal.fire({
                title: '配置保存成功',
                text: 'API配置已更新',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
        
        // 加载Token数据
        async function loadTokenData() {
            try {
                // 显示加载中
                Swal.fire({
                    title: '加载中',
                    text: '正在获取Token数据...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // 获取Token列表
                const tokenResponse = await api.get(apiConfig.tokenList);
                
                if (tokenResponse.data.code === 200) {
                    tokenData = tokenResponse.data.data;
                    
                    // 处理用户信息
                    const userPromises = tokenData.map(async (item) => {
                        const userInfo = await fetchUserInfo(item.token);
                        item.userName = userInfo.data?.userName || '未设置昵称';
                        item.status = userInfo.code === 0 ? 'active' : 'inactive';
                        return item;
                    });
                    
                    // 等待所有用户信息获取完成
                    const updatedData = await Promise.all(userPromises);
                    
                    // 更新表格数据
                    tokenDataTable.clear().rows.add(updatedData).draw();
                    
                    // 更新统计信息
                    updateStatistics(updatedData);
                    
                    // 更新最后更新时间
                    document.getElementById('lastUpdated').textContent = new Date().toLocaleString('zh-CN');
                    
                    // 显示成功消息
                    Swal.close();
                    Swal.fire({
                        title: '加载成功',
                        text: `共加载 ${updatedData.length} 条Token数据`,
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                } else {
                    throw new Error(tokenResponse.data.message || '获取数据失败');
                }
            } catch (error) {
                console.error('加载Token数据失败:', error);
                Swal.close();
            }
        }
        
        // 获取用户信息
        async function fetchUserInfo(token) {
            try {
                const response = await api.post(apiConfig.userInfo, 
                    new URLSearchParams({ token }), // 使用x-www-form-urlencoded格式
                    {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'User-Agent': 'Mozilla/5.0 (Android)...'
                        },
                        timeout: 5000 // 5秒超时
                    }
                );
                
                // 统一错误处理
                if (response.data.code === 0) {
                    return {
                        code: 0,
                        data: {
                            userId: response.data.data.userId || '未返回',
                            userName: response.data.data.userName || '未设置昵称'
                        }
                    };
                } else if (response.data.msg === '未登录') {
                    return { code: -1, msg: '无效Token' };
                } else {
                    return { 
                        code: response.data.code || -999,
                        msg: response.data.msg || '未知错误'
                    };
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                return { code: -999, msg: '请求失败或无响应' };
            }
        }
        
        // 更新统计信息
        function updateStatistics(data) {
            const total = data.length;
            const active = data.filter(item => item.status === 'active').length;
            const inactive = total - active;
            
            document.getElementById('totalUsers').textContent = total;
            document.getElementById('activeTokens').textContent = active;
            document.getElementById('inactiveTokens').textContent = inactive;
        }
        
        // 计算时间差
        function calculateDaysDiff(createTime) {
            const now = new Date();
            const created = new Date(createTime);
            const diffTime = Math.abs(now - created);
            return Math.floor(diffTime / (1000 * 60 * 60 * 24));
        }
        
        // 获取当前密钥
        async function fetchCurrentKey() {
            try {
                const response = await api.get(apiConfig.currentKey);
                
                if (response.data.code === 200) {
                    document.getElementById('currentKey').value = response.data.data;
                } else {
                    throw new Error(response.data.message || '获取密钥失败');
                }
            } catch (error) {
                console.error('获取当前密钥失败:', error);
                document.getElementById('currentKey').value = '获取密钥失败';
            }
        }
        
        // 复制当前密钥
        function copyCurrentKey() {
            const keyInput = document.getElementById('currentKey');
            keyInput.select();
            document.execCommand('copy');
            
            // 显示复制成功提示
            Swal.fire({
                title: '复制成功',
                text: '密钥已复制到剪贴板',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
        
        // 生成新密钥
        function generateNewKey() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let key = '';
            for (let i = 0; i < 32; i++) {
                key += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            document.getElementById('currentKey').value = key;
            
            // 显示生成成功提示
            Swal.fire({
                title: '生成成功',
                text: '新密钥已生成，请点击"更新密钥"保存',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }
        
        // 更新密钥
        async function updateKey() {
            const newKey = document.getElementById('currentKey').value;
            
            if (!newKey || newKey.length < 32) {
                Swal.fire({
                    title: '密钥无效',
                    text: '密钥长度必须至少为32位',
                    icon: 'error'
                });
                return;
            }
            
            try {
                // 显示确认对话框
                const result = await Swal.fire({
                    title: '确认更新密钥',
                    text: '更新密钥将影响所有相关服务，是否继续？',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '确认更新',
                    cancelButtonText: '取消',
                    confirmButtonColor: '#34a853',
                    cancelButtonColor: '#ea4335'
                });
                
                if (result.isConfirmed) {
                    // 发送更新请求
                    const response = await api.put(`http://localhost/token/updateKey/1?key=${newKey}`);
                    
                    if (response.data.code === 200) {
                        Swal.fire({
                            title: '更新成功',
                            text: '密钥已成功更新',
                            icon: 'success'
                        });
                    } else {
                        throw new Error(response.data.message || '更新失败');
                    }
                }
            } catch (error) {
                console.error('更新密钥失败:', error);
            }
        }
        
        // 添加新Token
        function addNewToken() {
            Swal.fire({
                title: '添加新Token',
                html: `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">用户名</label>
                            <input type="text" id="newUserName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Token值</label>
                            <input type="text" id="newToken" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                        </div>
                        <div class="text-sm text-gray-500">
                            <i class="fa fa-info-circle text-primary mr-1"></i> Token长度至少为32位
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '添加',
                cancelButtonText: '取消',
                focusConfirm: false,
                preConfirm: () => {
                    const userName = document.getElementById('newUserName').value;
                    const token = document.getElementById('newToken').value;
                    
                    if (!userName) {
                        Swal.showValidationMessage('请输入用户名');
                    } else if (!token || token.length < 32) {
                        Swal.showValidationMessage('Token长度必须至少为32位');
                    }
                    
                    return { userName, token };
                }
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        // 发送添加请求
                        const response = await api.post('http://localhost/token/add', {
                            token: result.value.token,
                            userName: result.value.userName,
                            createTime: new Date().toISOString()
                        });
                        
                        if (response.data.code === 200) {
                            Swal.fire({
                                title: '添加成功',
                                text: '新Token已成功添加',
                                icon: 'success'
                            });
                            
                            // 刷新数据
                            loadTokenData();
                        } else {
                            throw new Error(response.data.message || '添加失败');
                        }
                    } catch (error) {
                        console.error('添加Token失败:', error);
                    }
                }
            });
        }
        
        // 更新Token
        function updateToken(id) {
            const tokenInfo = tokenData.find(item => item.id === id);
            
            if (!tokenInfo) {
                Swal.fire({
                    title: '错误',
                    text: '找不到该Token信息',
                    icon: 'error'
                });
                return;
            }
            
            Swal.fire({
                title: '更新Token',
                html: `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">用户名</label>
                            <input type="text" id="updateUserName" value="${tokenInfo.userName}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Token值</label>
                            <input type="text" id="updateToken" value="${tokenInfo.token}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30">
                        </div>
                        <div class="text-sm text-gray-500">
                            <i class="fa fa-info-circle text-primary mr-1"></i> Token长度至少为32位
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '更新',
                cancelButtonText: '取消',
                focusConfirm: false,
                preConfirm: () => {
                    const token = document.getElementById('updateToken').value;
                    
                    if (!token || token.length < 32) {
                        Swal.showValidationMessage('Token长度必须至少为32位');
                    }
                    
                    return { token };
                }
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        // 发送更新请求
                        const response = await api.put(`http://localhost/token/update/${id}`, {
                            id: id,
                            token: result.value.token,
                            key: "281317361"
                        });
                        
                        if (response.data.code === 200) {
                            Swal.fire({
                                title: '更新成功',
                                text: 'Token已成功更新',
                                icon: 'success'
                            });
                            
                            // 刷新数据
                            loadTokenData();
                        } else {
                            throw new Error(response.data.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新Token失败:', error);
                    }
                }
            });
        }
        
        // 删除Token
        function deleteToken(id) {
            const tokenInfo = tokenData.find(item => item.id === id);
            
            if (!tokenInfo) {
                Swal.fire({
                    title: '错误',
                    text: '找不到该Token信息',
                    icon: 'error'
                });
                return;
            }
            
            Swal.fire({
                title: '确认删除',
                html: `
                    <div class="space-y-3">
                        <div class="text-sm text-gray-600">
                            您确定要删除以下Token吗？
                        </div>
                        <div class="p-3 bg-neutral rounded-lg">
                            <div class="font-medium">用户名: ${tokenInfo.userName}</div>
                            <div class="token-text mt-1">Token: ${tokenInfo.token}</div>
                        </div>
                        <div class="text-sm text-danger">
                            <i class="fa fa-exclamation-triangle mr-1"></i> 删除后数据将无法恢复
                        </div>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                confirmButtonColor: '#ea4335',
                cancelButtonColor: '#999'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        // 发送删除请求
                        const response = await api.delete(`http://localhost/token/delete/${id}`);
                        
                        if (response.data.code === 200) {
                            Swal.fire({
                                title: '删除成功',
                                text: 'Token已成功删除',
                                icon: 'success'
                            });
                            
                            // 刷新数据
                            loadTokenData();
                        } else {
                            throw new Error(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除Token失败:', error);
                    }
                }
            });
        }
        
        // 批量更新Tokens
        function batchUpdateTokens() {
            Swal.fire({
                title: '确认批量更新',
                text: '这将为所有即将过期或已过期的Token生成新的Token，是否继续？',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确认更新',
                cancelButtonText: '取消',
                confirmButtonColor: '#34a853',
                cancelButtonColor: '#ea4335'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        // 找出需要更新的Tokens (使用超过15天的)
                        const tokensToUpdate = tokenData.filter(item => {
                            const days = calculateDaysDiff(item.createTime);
                            return days >= 15;
                        });
                        
                        if (tokensToUpdate.length === 0) {
                            Swal.fire({
                                title: '无需更新',
                                text: '所有Token都处于有效期内',
                                icon: 'info'
                            });
                            return;
                        }
                        
                        // 显示进度对话框
                        const progressDialog = Swal.fire({
                            title: '批量更新中',
                            html: `正在更新 ${tokensToUpdate.length} 个Token...`,
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            willOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        // 批量更新请求
                        const updatePromises = tokensToUpdate.map(async (item) => {
                            // 生成新Token
                            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                            let newToken = '';
                            for (let i = 0; i < 32; i++) {
                                newToken += chars.charAt(Math.floor(Math.random() * chars.length));
                            }
                            
                            // 更新Token
                            return api.put(`http://localhost/token/update/${item.id}`, {
                                token: newToken,
                                userName: item.userName
                            });
                        });
                        
                        // 等待所有更新完成
                        const results = await Promise.all(updatePromises);
                        
                        // 检查是否所有更新都成功
                        const failedUpdates = results.filter(result => result.data.code !== 200);
                        
                        progressDialog.close();
                        
                        if (failedUpdates.length === 0) {
                            Swal.fire({
                                title: '更新成功',
                                text: `成功更新 ${tokensToUpdate.length} 个Token`,
                                icon: 'success'
                            });
                            
                            // 刷新数据
                            loadTokenData();
                        } else {
                            throw new Error(`部分Token更新失败，成功: ${results.length - failedUpdates.length}，失败: ${failedUpdates.length}`);
                        }
                    } catch (error) {
                        console.error('批量更新Tokens失败:', error);
                        Swal.fire({
                            title: '更新失败',
                            text: error.message,
                            icon: 'error'
                        });
                    }
                }
            });
        }
    </script>
</body>
</html>