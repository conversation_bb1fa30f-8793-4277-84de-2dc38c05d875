<template>
  <div class="app-container">
    <aside v-if="!isLoginPage" :class="['sidebar', { collapsed: isSidebarCollapsed }]">
      <button class="toggle-button" @click="toggleSidebar">
        {{ isSidebarCollapsed ? '➡️' : '⬅️' }}
      </button>
      <div class="logo-container">
        <img src="https://img.tuxiangyan.com/zb_users/upload/2023/02/202302091675904133489339.jpg" alt="Logo" class="logo-img" />
        <h2 class="logo-text" v-if="!isSidebarCollapsed">🏬 管理系统</h2>
      </div>
      <nav class="menu">
        <router-link to="/" class="menu-item" active-class="active">
          <el-icon><HomeFilled /></el-icon> <span v-if="!isSidebarCollapsed">首页  </span>
        </router-link>
        <router-link to="/user" class="menu-item" active-class="active">
          <el-icon><User /></el-icon> <span v-if="!isSidebarCollapsed">用户管理</span>
        </router-link>
        <router-link to="/shop" class="menu-item" active-class="active">
          <el-icon><Shop /></el-icon> <span v-if="!isSidebarCollapsed">商品管理</span>
        </router-link>
        <router-link to="/orders" class="menu-item" active-class="active">
          <el-icon><Tickets /></el-icon> <span v-if="!isSidebarCollapsed">订单管理</span>
        </router-link>
      </nav>
    </aside>
    <div class="main-content">
      <header v-if="!isLoginPage" class="topbar">
        <h1>宿舍小卖部管理系统</h1>
      </header>
      <main class="content">
        <transition name="fade-slide" mode="out-in">
          <router-view></router-view>
        </transition>
      </main>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { ref, computed } from 'vue';
import { HomeFilled, User, Shop, Tickets } from '@element-plus/icons-vue'

const router = useRouter();
const route = useRoute();
const isSidebarCollapsed = ref(false);

// 判断当前页面是否是登录页面
const isLoginPage = computed(() => route.path === '/login');

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};
</script>

<style lang="scss">
.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #fdfbfb, #bdbdbd);

  .sidebar {
    width: 250px;
    background: linear-gradient(135deg, #09d788, #fad0c4);
    color: #fff;
    padding: 20px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 0 20px 20px 0;
    transition: width 0.3s ease;
    position: relative;

    &.collapsed {
      width: 80px;
    }

    .toggle-button {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #fff;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      font-size: 18px;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }

    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-bottom: 20px;
      margin-top: 60px;

      .logo-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      }

      .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        animation: bounce 2s infinite;
      }
    }

    .menu {
      display: flex;
      flex-direction: column;

      .menu-item {
        display: flex;
        align-items: center;
        color: #fff;
        text-decoration: none;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        font-size: 18px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

        i {
          margin-right: 10px;
          font-size: 24px;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.4);
          transform: scale(1.1);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
      }

      .menu-item.active {
        background: rgba(255, 255, 255, 0.6);
        color: #ffd04b;
        font-weight: bold;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        transform: scale(1.1);
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .topbar {
      background-color: #e4efee;
      padding: 15px 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 0 0 20px 20px;

      h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
        text-align: center;
      }
    }

    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background-color: #1995a2;
      border-radius: 20px;
      margin: 20px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      animation: fadeIn 1s ease;
      position: relative;
    }
  }
}

// 丝滑路由切换动画
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: opacity 0.5s cubic-bezier(.55,0,.1,1), transform 0.5s cubic-bezier(.55,0,.1,1);
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(40px) scale(0.98);
}
.fade-slide-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}
.fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-40px) scale(0.98);
}

.icon-home::before {
  content: "🏠";
}

.icon-user::before {
  content: "👤";
}

.icon-shop::before {
  content: "🏪";
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>