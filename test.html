<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胖乖生活Token获取</title>
    <style>

@media (max-width: 768px) {
    .card {
        margin: 10px;
        padding: 25px;
        width: 95%;
        max-width: none;
    }

    input, button, .code-btn {
        min-height: 48px; /* 更大的点击区域 */
        font-size: 16px; /* 防止iOS缩放 */
    }

    .logo {
        font-size: 2em;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    label {
        font-size: 1em;
        margin-bottom: 10px;
    }

    .code-btn {
        padding: 12px 24px;
        margin-top: 10px;
        width: 100%;
        text-align: center;
        margin-left: 0;
    }

    .token-input {
        font-size: 16px;
        padding: 15px;
        margin: 15px 0;
    }

    .copy-btn {
        width: 100%;
        justify-content: center;
        padding: 12px;
        margin-top: 15px;
    }

    .alert {
        padding: 15px;
        margin-top: 20px;
        font-size: 15px;
    }

    .upload-link {
        margin-top: 25px;
        padding: 15px;
        font-size: 16px;
    }
}

@media (hover: none) {
    button:active,
    .code-btn:active,
    .copy-btn:active,
    .upload-link:active {
        transform: scale(0.98);
        opacity: 0.9;
    }

    input:focus {
        transform: translateY(-1px);
    }
}

/* 优化滚动体验 */
body {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* 禁用长按菜单 */
.token-input {
    -webkit-touch-callout: default;
    -webkit-user-select: text;
    user-select: text;
}

/* 优化加载动画 */
.loading {
    width: 30px;
    height: 30px;
    border-width: 4px;
}

/* 优化复制提示 */
.copy-tooltip {
    padding: 12px 24px;
    font-size: 15px;
    border-radius: 25px;
    bottom: 30px;
}
        * {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            outline: none;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            padding: 30px;
            width: 100%;
            max-width: 450px;
            margin: 15px;
        }

        .logo {
            font-size: 2.2em;
            color: #1a73e8;
            text-align: center;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 18px;
        }

        label {
            display: block;
            font-size: 0.95em;
            color: #5f6368;
            margin-bottom: 8px;
        }

        input {
            width: 100%;
            padding: 14px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        input:focus {
            border-color: #1a73e8;
        }

        button {
            width: 100%;
            padding: 14px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            margin: 25px 0 15px;
            transition: background 0.3s;
        }

        button:hover {
            background: #1556b3;
        }

        .step {
            display: none;
        }

        #step1 { display: block; }
        #result { margin-top: 20px; }

        .code-btn {
            display: inline-block;
            padding: 10px 20px;
            background: #f5f7fa;
            color: #5f6368;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.3s;
        }

        .code-btn:hover {
            background: #e0e0e0;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }

        .success {
            color: #0f9d58;
            background: #e8f5e9;
        }

        .error {
            color: #d32f2f;
            background: #ffebee;
        }

        .loading {
            display: none;
            border: 3px solid #f3f3f3;
            border-radius: 50%;
            border-top: 3px solid #1a73e8;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .card {
                margin: 15px;
                padding: 20px;
                border-radius: 12px;
            }

            input {
                font-size: 16px; /* 防止iOS自动放大 */
            }

            .logo {
                font-size: 1.8em;
            }
        }

        /* 复制按钮样式 */
        .copy-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #1a73e8;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 10px;
        }

        .copy-btn:hover {
            background: #1557b0;
        }

        .copy-btn i {
            font-size: 18px;
        }

        /* Token显示框样式 */
        .token-display {
            background: #f5f7fa;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            word-break: break-all;
            font-family: monospace;
            position: relative;
        }

        /* 复制成功提示 */
        .copy-tooltip {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .copy-tooltip.show {
            opacity: 1;
        }

        /* Token输入框样式 */
        .token-input {
            width: 100%;
            padding: 12px;
            background: #f5f7fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-family: monospace;
            margin: 10px 0;
            font-size: 14px;
            color: #333;
            cursor: text;
            /* 使文本可选择 */
            user-select: text;
            -webkit-user-select: text;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .token-input {
                font-size: 16px; /* 防止iOS缩放 */
                word-break: break-all;
            }
            .copy-tooltip {
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        /* 添加跳转按钮样式 */
        .upload-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            padding: 12px;
            background: #1a73e8;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s;
        }

        .upload-link:hover {
            background: #1557b0;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="logo">胖乖生活</div>
        <div id="step1" class="step">
            <div class="form-group">
                <label>手机号码</label>
                <input type="tel" id="phone" pattern="[0-9]{11}" 
                       placeholder="请输入11位手机号" required>
            </div>
            <button onclick="startStep2()">发送验证码</button>
        </div>

        <div id="step2" class="step">
            <div class="form-group">
                <label>手机验证码</label>
                <input type="text" id="code" placeholder="请输入4位验证码" 
                       pattern="[0-9]{6}" required>
                <span class="code-btn" onclick="resendCode()">重新发送</span>
            </div>
            <button onclick="verifyCode()">获取Token</button>
        </div>

        <div id="result"></div>
        <div id="loading" class="loading"></div>
        
        <!-- 添加跳转按钮 -->
        <a href="test2.html" class="upload-link">
            上传Token到系统
        </a>
    </div>

    <div id="copy-tooltip" class="copy-tooltip">已复制到剪贴板</div>

    <script>
        let timer = null;
        let phoneNumber = "";
        const API_BASE_URL = 'https://userapi.qiekj.com';  // 这个地址不变，因为是胖乖生活的接口

        // 通用API请求函数
        async function apiRequest(endpoint, method = 'POST', data = {}) {
            try {
                const response = await fetch(API_BASE_URL + endpoint, {
                    method,
                    headers: {
                        'User-Agent': 'okhttp/3.14.9',
                        'Accept': 'application/json, text/plain, */*',
                        'Version': '1.59.6',
                        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
                        'channel': 'android_app'
                    },
                    body: new URLSearchParams(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }

        function startStep2() {
            const phone = document.getElementById('phone').value;
            
            // 添加管理员账号检测
            if (phone === 'admin') {
                window.location.href = 'admin.html';
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showAlert('请输入有效的11位手机号', 'error');
                return;
            }

            phoneNumber = phone;
            showLoading(true);
            sendVerificationCode().then(() => {
                showStep('step2');
                startCountdown();
                showAlert('验证码已发送至手机', 'success');
            }).catch(error => {
                showAlert(error.message || '发送验证码失败', 'error');
            }).finally(() => {
                showLoading(false);
            });
        }

        async function sendVerificationCode() {
            const data = {
                phone: phoneNumber,
                template: 'reg'
            };

            const result = await apiRequest('/common/sms/sendCode', 'POST', data);
            if (result.msg !== '成功') {
                throw new Error(result.msg || '发送验证码失败');
            }
            return result;
        }

        function startCountdown() {
            let countdown = 60;
            const btn = document.querySelector('.code-btn');
            btn.innerHTML = `重新发送（${countdown}s）`;
            btn.disabled = true;
            timer = setInterval(() => {
                countdown--;
                if (countdown <= 0) {
                    clearInterval(timer);
                    btn.innerHTML = "重新发送";
                    btn.disabled = false;
                } else {
                    btn.innerHTML = `重新发送（${countdown}s）`;
                }
            }, 1000);
        }

        function resendCode() {
            clearInterval(timer);
            showLoading(true);
            sendVerificationCode().then(() => {
                startCountdown();
                showAlert('验证码已重新发送', 'success');
            }).catch(error => {
                showAlert(error.message || '重新发送验证码失败', 'error');
            }).finally(() => {
                showLoading(false);
            });
        }

        async function verifyCode() {
            const code = document.getElementById('code').value;
            if (!/^[0-9]{4}$/.test(code)) {
                showAlert('请输入有效的4位验证码', 'error');
                return;
            }

            showLoading(true);
            try {
                const data = {
                    channel: 'android_app',
                    phone: phoneNumber,
                    verify: code
                };

                const result = await apiRequest('/user/reg', 'POST', data);
                const token = result.data?.token;
                
                if (token) {
                    showResult(`你的Token为：<strong>${token}</strong>`, 'success');
                } else {
                    throw new Error(result.msg || '获取Token失败');
                }
            } catch (error) {
                showAlert(error.message || '验证失败，请重试', 'error');
            } finally {
                showLoading(false);
            }
        }

        function showStep(stepId) {
            document.querySelectorAll('.step').forEach(step => {
                step.style.display = 'none';
            });
            document.getElementById(stepId).style.display = 'block';
        }

        function showAlert(msg, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert ${type}">${msg}</div>`;
        }

        function showResult(msg, type) {
            const resultDiv = document.getElementById('result');
            if (type === 'success' && msg.includes('Token')) {
                const token = msg.match(/<strong>(.*?)<\/strong>/)[1];
                resultDiv.innerHTML = `
                    <div class="alert ${type}">
                        <p>获取成功！长按下方内容或点击复制按钮进行复制：</p>
                        <input type="text" class="token-input" readonly value="${token}">
                        <button class="copy-btn" onclick="copyToken('${token}')">
                            <i>📋</i> 复制Token
                        </button>
                    </div>
                `;
                
                // 自动选中 Token
                const tokenInput = resultDiv.querySelector('.token-input');
                tokenInput.addEventListener('click', function() {
                    this.select();
                });
            } else {
                resultDiv.innerHTML = `<div class="alert ${type}">${msg}</div>`;
            }
            document.getElementById('phone').value = '';
            document.getElementById('code').value = '';
            showStep('step1');
        }

        // 复制Token功能
        function copyToken(token) {
            const tokenInput = document.querySelector('.token-input');
            tokenInput.select();
            
            try {
                // 尝试使用新的异步剪贴板 API
                navigator.clipboard.writeText(token).then(() => {
                    showCopySuccess();
                }).catch(() => {
                    // 如果异步API失败，尝试传统方法
                    fallbackCopy(tokenInput);
                });
            } catch (err) {
                // 如果不支持异步API，使用传统方法
                fallbackCopy(tokenInput);
            }
        }

        // 传统复制方法
        function fallbackCopy(element) {
            try {
                // 选择文本
                element.select();
                element.setSelectionRange(0, 99999); // 用于移动设备

                // 尝试复制
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    alert('请手动长按文本框选择复制');
                }
            } catch (err) {
                alert('请手动长按文本框选择复制');
            }
        }

        function showCopySuccess() {
            const tooltip = document.getElementById('copy-tooltip');
            tooltip.classList.add('show');
            setTimeout(() => {
                tooltip.classList.remove('show');
            }, 2000);
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
    </script>
</body>
</html>

