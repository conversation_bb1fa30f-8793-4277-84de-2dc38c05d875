<template>
  <div class="login-container">
    <div class="login-card">
      <div class="brand">
        <img src="https://img.tuxiangyan.com/uploads/allimg/220101/1_010119515532F.jpg" alt="Logo" />
        <h2>宿舍小卖部</h2>
      </div>
      <p class="welcome-text">欢迎来到小卖部系统</p>
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-item">
          <input
            v-model="loginForm.username"
            type="text"
            placeholder="请输入用户名"
            class="input"
            required
          />
        </div>
        <div class="form-item">
          <input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            class="input"
            required
          />
        </div>
        <div class="form-item">
          <button type="submit" class="login-button">登录</button>
        </div>
      </form>
      <p class="footer-text">© 2025  宿舍小卖部. 保留所有权利.</p>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import { useRouter } from 'vue-router';
import Swal from 'sweetalert2';

const router = useRouter();
const loginForm = reactive({
  username: '',
  password: '',
});

const handleLogin = () => {
  if (loginForm.username === 'admin' && loginForm.password === '123456') {
    Swal.fire('登录成功', '欢迎回来！', 'success');
    sessionStorage.setItem('isAuthenticated', 'true'); // 模拟登录状态
    router.push('/index'); // 使用 Vue Router 跳转到首页
  } else {
    Swal.fire('登录失败', '用户名或密码错误', 'error');
  }
};
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  // background: url('https://img.dexbug.com/i/2025/05/26/zdj8a2.png') no-repeat center center;
  background-size: cover;
  font-family: 'Arial', sans-serif;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(50, 205, 50, 0.8));
    z-index: 1;
  }

  .login-card {
    position: relative;
    z-index: 2;
    width: 360px;
    padding: 40px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;

    .brand {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin-bottom: 10px;
      }

      h2 {
        font-size: 28px;
        color: #333;
        font-weight: bold;
      }
    }

    .welcome-text {
      margin-bottom: 30px;
      font-size: 16px;
      color: #666;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .form-item {
        .input {
          width: 100%;
          padding: 12px;
          font-size: 14px;
          border: 1px solid #ddd;
          border-radius: 8px;
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
          transition: border-color 0.3s;

          &:focus {
            border-color: #228b22;
            outline: none;
          }
        }
      }

      .login-button {
        width: 105%;
        padding: 12px;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        background: linear-gradient(135deg, #228b22, #32cd32);
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background 0.3s, transform 0.2s;

        &:hover {
          background: linear-gradient(135deg, #32cd32, #228b22);
          transform: scale(1.02);
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .footer-text {
      margin-top: 20px;
      font-size: 12px;
      color: #aaa;
    }
  }
}
</style>