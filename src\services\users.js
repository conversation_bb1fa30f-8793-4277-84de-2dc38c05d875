import axios from 'axios';

const BASE_URL = 'http://localhost:8081';


// 创建一个 axios 实例
const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      data: config.data
    });
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    console.log('收到响应:', response);
    return response;
  },
  error => {
    console.error('响应错误:', error);
    return Promise.reject(error);
  }
);

const userService = {
  // 获取所有用户
  getAllUsers() {
    return instance.get('/user/selectAll');
  },

  // 添加用户
  addUser(userData) {
    // 确保密码是字符串类型
    const data = {
      ...userData,
      password: String(userData.password)
    };
    return instance.post('/user/add', data);
  },

  // 更新用户
  updateUser(userData) {
    // 确保密码是字符串类型
    const data = {
      ...userData,
      id: Number(userData.id),
      password: String(userData.password)
    };
    return instance.put('/user/update', data);
  },

  // 删除用户
  deleteUser(userId) {
    return instance.delete(`/user/delete/${userId}`);
  },

  // 创建账户
  createAccount(userId, money) {
    return instance.post('/account/add', {
      userId: String(userId),
      money: String(money)
    });
  },

  // 获取账户信息
  getAccountInfo(userId) {
    return instance.get(`/account/${userId}`);
  },

  // 充值
  rechargeMoney(userId, money) {
    return instance.put('/account/money', {
      userId: String(userId),
      money: String(money)
    });
  }
};

export default userService; 