<template>
  <div class="shop-manager">
    <header class="header">
      <h1>商店管理</h1>
      <div class="actions">
        <button @click="showAddProductDialog" class="add-button">添加商品</button>
        <input
          type="text"
          v-model="searchQuery"
          placeholder="🔍 搜索商品..."
          class="search-box"
        />
      </div>
    </header>

    <!-- Loading 状态 -->
    <div v-if="loading" class="loading">
      加载中...
    </div>

    <table v-else class="shop-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>商品名称</th>
          <th>价格</th>
          <th>描述</th>
          <th>图片</th>
          <th>分类</th>
          <th>库存</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="shop in filteredShops" :key="shop.id">
          <td>{{ shop.id }}</td>
          <td>{{ shop.name }}</td>
          <td>{{ shop.price }}</td>
          <td>{{ shop.description }}</td>
          <td>
            <img :src="shop.image" alt="商品图片" class="shop-img" />
          </td>
          <td>{{ shop.category }}</td>
          <td>{{ shop.num }}</td>
          <td>
            <button @click="editProduct(shop)" class="edit-button">编辑</button>
            <button @click="confirmDeleteShop(shop.id)" class="delete-button">删除</button>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 无数据提示 -->
    <p v-if="!loading && !filteredShops.length" class="no-shops">没有找到商品</p>

    <!-- 分页控件 -->
    <div class="pagination" v-if="totalPages > 1">
      <button 
        @click="handlePageChange(currentPage - 1)" 
        :disabled="currentPage === 1"
        class="page-button"
      >
        上一页
      </button>
      <span class="page-info">第 {{ currentPage }} / {{ totalPages }} 页</span>
      <button 
        @click="handlePageChange(currentPage + 1)" 
        :disabled="currentPage >= totalPages"
        class="page-button"
      >
        下一页
      </button>
      <select v-model="pageSize" class="page-size-select">
        <option :value="6">6条/页</option>
        <option :value="10">10条/页</option>
        <option :value="20">20条/页</option>
      </select>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';
import Swal from 'sweetalert2';
import productService from '../services/products';

const searchQuery = ref('');
const shops = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(6);
const allShops = ref([]); // 存储所有商品数据

// 添加商品的表单数据
const newProduct = ref({
  name: '',
  image: '',
  description: '',
  price: 0,
  category: '',
  num: 0
});

// 页面加载时获取数据
onMounted(() => {
  fetchShops();
});

const defaultAvatar = 'https://s1.chu0.com/src/img/png/8f/8fd48d4c10964ff39a4593f5236ccb25.png?e=2051020800&token=1srnZGLKZ0Aqlz6dk7yF4SkiYf4eP-YrEOdM1sob:uR2g21KasU_IkLJNha3wvABl_qM='; // 默认头像

// 获取所有商店数据
const fetchShops = async () => {
  loading.value = true;
  try {
    const response = await productService.getAllProducts();
    if (response.data.code === 200) {
      allShops.value = response.data.data;
    } else {
      Swal.fire('错误', response.data.msg || '获取数据失败', 'error');
    }
  } catch (error) {
    console.error('请求失败:', error);
    Swal.fire('错误', '网络请求失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 过滤商店列表
const filteredShops = computed(() =>
  allShops.value.filter(
    (shop) =>
      shop.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      shop.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
);

// 分页后的商品列表
const pagedShops = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredShops.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => 
  Math.ceil(filteredShops.value.length / pageSize.value)
);

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
};

// 搜索时重置到第一页
watch(searchQuery, () => {
  currentPage.value = 1;
});

// 添加商品
const addProduct = async (product) => {
  try {
    const response = await productService.addProduct(product);
    if (response.data.code === 200) {
      Swal.fire('成功', '商品添加成功', 'success');
      await fetchShops(); // 刷新商品列表
    } else {
      Swal.fire('错误', response.data.msg || '添加失败', 'error');
    }
  } catch (error) {
    console.error('添加商品失败:', error);
    Swal.fire('错误', '网络请求失败', 'error');
  }
};

// 更新商品
const updateProduct = async (product) => {
  try {
    const response = await productService.updateProduct(product);
    if (response.data.code === 200) {
      Swal.fire('成功', '商品更新成功', 'success');
      await fetchShops(); // 刷新商品列表
    } else {
      Swal.fire('错误', response.data.msg || '更新失败', 'error');
    }
  } catch (error) {
    console.error('更新商品失败:', error);
    Swal.fire('错误', '网络请求失败', 'error');
  }
};

// 编辑商品
const editProduct = (product) => {
  Swal.fire({
    title: '编辑商品',
    html: `
      <input id="name" class="swal2-input" placeholder="商品名称" value="${product.name}">
      <input id="price" class="swal2-input" placeholder="价格" type="number" value="${product.price}">
      <input id="description" class="swal2-input" placeholder="描述" value="${product.description}">
      <input id="image" class="swal2-input" placeholder="图片URL" value="${product.image}">
      <input id="category" class="swal2-input" placeholder="分类" value="${product.category}">
      <input id="num" class="swal2-input" placeholder="库存" type="number" value="${product.num}">
    `,
    focusConfirm: false,
    preConfirm: () => {
      const updatedProduct = {
        id: product.id,
        name: document.getElementById('name').value,
        price: Number(document.getElementById('price').value),
        description: document.getElementById('description').value,
        image: document.getElementById('image').value,
        category: document.getElementById('category').value,
        num: document.getElementById('num').value
      };
      return updateProduct(updatedProduct);
    }
  }).then((result) => {
    if (result.isConfirmed) {
      if (result.value.data.code === 200) {
        Swal.fire('成功', '商品已更新', 'success');
        fetchShops();
      } else {
        Swal.fire('错误', result.value.data.msg || '更新失败', 'error');
      }
    }
  });
};

// 确认删除商店
const confirmDeleteShop = (id) => {
  Swal.fire({
    title: '确定删除该商品吗？',
    text: '删除后将无法恢复！',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: '删除',
    cancelButtonText: '取消',
  }).then(async (result) => {
    if (result.isConfirmed) {
      try {
        const response = await productService.deleteProduct(id);
        if (response.data.code === 200) {
          Swal.fire('删除成功', '商品已被删除', 'success');
          await fetchShops(); // 刷新商品列表
        } else {
          Swal.fire('错误', response.data.msg || '删除失败', 'error');
        }
      } catch (error) {
        console.error('删除商品失败:', error);
        Swal.fire('错误', '网络请求失败', 'error');
      }
    }
  });
};

const categories = ['美食饮料', '文具'];

// 显示添加商品对话框
const showAddProductDialog = () => {
  Swal.fire({
    title: '添加新商品',
    html: `
      <input id="name" class="swal2-input" placeholder="商品名称">
      <input id="price" class="swal2-input" placeholder="价格" type="number">
      <input id="description" class="swal2-input" placeholder="描述">
      <input id="image" class="swal2-input" placeholder="图片URL">
      <input id="category" class="swal2-input" placeholder="分类">
      <input id="num" class="swal2-input" placeholder="库存" type="number">
    `,
    focusConfirm: false,
    preConfirm: () => {
      const newProduct = {
        name: document.getElementById('name').value,
        price: Number(document.getElementById('price').value),
        description: document.getElementById('description').value,
        image: document.getElementById('image').value,
        category: document.getElementById('category').value,
        num: document.getElementById('num').value
      };
      return addProduct(newProduct);
    }
  });
};
</script>

<style lang="scss" scoped>
.shop-manager {
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      font-size: 24px;
      color: #333;
    }

    .actions {
      display: flex;
      gap: 10px;

      .add-button {
        padding: 10px 20px;
        background-color: #4CAF50;
        color: #fff;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;

        &:hover {
          background-color: #45a049;
        }
      }

      .search-box {
        padding: 10px;
        font-size: 16px;
        border: 1px solid #ccc;
        border-radius: 8px;
        width: 300px;
      }
    }
  }

  .shop-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;

    th,
    td {
      padding: 15px;
      text-align: center;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f4f4f4;
      color: #333;
      font-weight: bold;
    }

    // 移除间隔颜色
    tr {
      background-color: #fff;

      &:hover {
        background-color: #f1f1f1;
      }
    }

    .shop-img {
      width: 50px;
      height: 50px;
      border-radius: 5px;
      object-fit: cover;
    }

    .owner-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .owner-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .delete-button {
      padding: 5px 10px;
      background-color: #f44336;
      color: #fff;
      border: none;
      border-radius: 5px;
      cursor: pointer;

      &:hover {
        background-color: #d32f2f;
      }
    }

    .edit-button {
      padding: 5px 10px;
      background-color: #2196F3;
      color: #fff;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      margin-right: 8px;

      &:hover {
        background-color: #1976D2;
      }
    }
  }

  .no-shops {
    text-align: center;
    color: #999;
    margin-top: 20px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 20px;

    .page-button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background: #4caf50;
      color: #fff;
      cursor: pointer;
      
      &:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      
      &:hover:not(:disabled) {
        background: #45a049;
      }
    }

    .page-info {
      font-size: 14px;
      color: #666;
    }

    .page-size-select {
      padding: 6px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      
      &:focus {
        outline: none;
        border-color: #4caf50;
      }
    }
  }

  .loading {
    text-align: center;
    padding: 20px;
    font-size: 16px;
    color: #666;
  }
}
</style>