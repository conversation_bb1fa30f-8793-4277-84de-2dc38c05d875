<template>
  <div class="order-manager">
    <h2>订单列表</h2>
    <table class="order-table">
      <thead>
        <tr>
          <th>订单ID</th>
          <th>用户名</th>
          <th>商品信息</th>
          <th>创建时间</th>
          <th>状态</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="order in orders" :key="order.orderId">
          <td>{{ order.orderId }}</td>
          <td>{{ order.username }}</td>
          <td>{{ order.orderData }}</td>
          <td>{{ formatDate(order.createTime) }}</td>
          <td>{{ getStatusText(order.status) }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const orders = ref([])

// 状态文本映射
const statusMap = {
  0: '待支付',
  1: '已支付',
  2: '已发货',
  3: '已完成',
  4: '已取消'
}
const getStatusText = (status) => statusMap[status] || '未知状态'

// 时间格式化
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取订单数据
const fetchOrders = async () => {
  try {
    const res = await fetch('/api/orders/all')
    const data = await res.json()
    orders.value = Array.isArray(data) ? data : []
  } catch (e) {
    orders.value = []
  }
}

onMounted(fetchOrders)
</script>

<style lang="scss" scoped>
.order-manager {
  padding: 24px;
}
.order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
  background: #fff;
  th, td {
    border: 1px solid #eee;
    padding: 8px 12px;
    text-align: center;
  }
  th {
    background: #f5f7fa;
    font-weight: bold;
  }
}
</style>