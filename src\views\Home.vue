<template>
  <div class="home-dashboard">
    <!-- 统计卡片 -->
    <div class="stat-cards">
      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>总订单数</span>
            <el-icon class="icon"><ShoppingCart /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.orderCount }}</div>
          <div class="label">订单总数</div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>商品数量</span>
            <el-icon class="icon"><Goods /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.productCount }}</div>
          <div class="label">在售商品</div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>用户数量</span>
            <el-icon class="icon"><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.userCount }}</div>
          <div class="label">注册用户</div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>今日订单</span>
            <el-icon class="icon"><Calendar /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <div class="number">{{ stats.todayOrders }}</div>
          <div class="label">今日新增</div>
        </div>
      </el-card>
    </div>

    <!-- 最近订单 -->
    <el-card class="recent-orders" shadow="hover">
      <template #header>
        <div class="section-header">
          <span>最近订单</span>
          <el-button type="primary" link @click="goToOrders">查看全部</el-button>
        </div>
      </template>
      <el-table :data="recentOrders" style="width: 100%">
        <el-table-column prop="orderId" label="订单ID" width="100" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column label="商品信息" min-width="300">
          <template #default="{ row }">
            <div class="order-items">
              <div v-for="item in parseOrderData(row.orderData)" :key="item.id" class="order-item">
                <el-image 
                  :src="item.image" 
                  :alt="item.name"
                  class="product-img"
                  :preview-src-list="[item.image]"
                  fit="cover"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="item-details">
                  <div class="item-name">{{ item.name }}</div>
                  <div class="item-price">￥{{ item.price }} × {{ item.quantity }}</div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 热门商品 -->
    <el-card class="hot-products" shadow="hover">
      <template #header>
        <div class="section-header">
          <span>热门商品</span>
          <el-button type="primary" link @click="goToProducts">查看全部</el-button>
        </div>
      </template>
      <div class="product-grid">
        <el-card 
          v-for="product in hotProducts" 
          :key="product.id" 
          class="product-card"
          shadow="hover"
        >
          <el-image 
            :src="product.image" 
            class="product-image"
            fit="cover"
          />
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">￥{{ product.price }}</div>
            <div class="product-stock">库存: {{ product.num }}</div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ShoppingCart, Goods, User, Calendar, Picture } from '@element-plus/icons-vue';
import orderService from '../services/orders';
import userService from '../services/users';
import productService from '../services/products';

const router = useRouter();
const stats = ref({
  orderCount: 0,
  productCount: 0,
  userCount: 0,
  todayOrders: 0
});

const recentOrders = ref([]);
const hotProducts = ref([]);

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取订单列表
    const ordersResponse = await orderService.getAllOrders();
    if (ordersResponse.data.code === 200) {
      const orders = ordersResponse.data.data || [];
      stats.value.orderCount = orders.length;
      
      // 计算今日订单
      const today = new Date().toISOString().split('T')[0];
      stats.value.todayOrders = orders.filter(order => 
        order.createTime.startsWith(today)
      ).length;

      // 获取最近5个订单
      recentOrders.value = orders.slice(0, 5);
    }

    // 获取商品列表
    const productsResponse = await productService.getAllProducts();
    if (productsResponse.data.code === 200) {
      const products = productsResponse.data.data || [];
      stats.value.productCount = products.length;
      
      // 获取前4个商品作为热门商品
      hotProducts.value = products.slice(0, 4);
    }

    // 获取用户列表
    const usersResponse = await userService.getAllUsers();
    if (usersResponse.data.code === 200) {
      const users = usersResponse.data.data || [];
      stats.value.userCount = users.length;
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 解析订单数据
const parseOrderData = (orderData) => {
  try {
    if (typeof orderData === 'string') {
      return JSON.parse(orderData);
    }
    return orderData;
  } catch (error) {
    console.error('解析订单数据失败:', error);
    return [];
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '待支付',
    1: '已支付',
    2: '已发货',
    3: '已完成',
    4: '已取消'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态类型
const getStatusType = (status) => {
  const statusTypeMap = {
    0: 'warning',
    1: 'primary',
    2: 'success',
    3: 'info',
    4: 'danger'
  };
  return statusTypeMap[status] || 'info';
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 页面跳转
const goToOrders = () => {
  router.push('/orders');
};

const goToProducts = () => {
  router.push('/products');
};

// 页面加载时获取数据
onMounted(() => {
  fetchStats();
});
</script>

<style lang="scss" scoped>
.home-dashboard {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .stat-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        color: #606266;

        .icon {
          font-size: 20px;
          color: #409EFF;
        }
      }

      .card-content {
        text-align: center;
        padding: 20px 0;

        .number {
          font-size: 36px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .recent-orders {
    .order-items {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .order-item {
        display: flex;
        align-items: center;
        gap: 12px;

        .product-img {
          width: 40px;
          height: 40px;
          border-radius: 4px;
        }

        .item-details {
          .item-name {
            font-weight: 500;
            margin-bottom: 4px;
          }

          .item-price {
            font-size: 13px;
            color: #909399;
          }
        }
      }
    }
  }

  .hot-products {
    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      padding: 10px;

      .product-card {
        .product-image {
          width: 100%;
          height: 150px;
          object-fit: cover;
          border-radius: 4px;
          margin-bottom: 12px;
        }

        .product-info {
          .product-name {
            font-weight: 500;
            margin-bottom: 8px;
          }

          .product-price {
            color: #f56c6c;
            font-size: 16px;
            margin-bottom: 4px;
          }

          .product-stock {
            font-size: 13px;
            color: #909399;
          }
        }
      }
    }
  }
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style> 