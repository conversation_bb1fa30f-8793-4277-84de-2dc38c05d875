import axios from 'axios';

const BASE_URL = 'http://localhost:8081'; // Adjust this based on your API base URL

// 创建一个 axios 实例
const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000, // 10 秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

const productService = {
  // 获取所有商品（分页）
  getProductsByPage(page = 1, pageSize = 6) {
    return axios.get(`${BASE_URL}/products/page`, {
      params: {
        page,
        pageSize
      }
    });
  },

  // 获取所有商品
  getAllProducts() {
    return axios.get(`${BASE_URL}/products/all`);
  },

  // 添加商品
  addProduct(product) {
    return axios.post(`${BASE_URL}/products/add`, product);
  },

  // 更新商品
  updateProduct(product) {
    return axios.post(`${BASE_URL}/products/update`, product);
  },

  // 删除商品
  deleteProduct(id) {
    return axios.delete(`${BASE_URL}/products/del/${id}`);
  }
};

export default productService;