<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token上传系统</title>
    <style>
        * {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            padding: 30px;
            width: 100%;
            max-width: 450px;
        }

        .title {
            text-align: center;
            color: #1a73e8;
            margin-bottom: 25px;
            font-size: 24px;
        }

        .step {
            display: none;
            margin-bottom: 20px;
        }

        .step.active {
            display: block;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            border-color: #1a73e8;
            outline: none;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #1557b0;
        }

        .qr-info {
            text-align: center;
            padding: 20px;
            background: #f5f7fa;
            border-radius: 8px;
            margin: 20px 0;
        }

        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }

        .success {
            color: #0f9d58;
            background: #e8f5e9;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
        }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-material-ui/material-ui.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1 class="title">Token上传系统</h1>
        
        <!-- 密钥输入步骤 -->
        <div id="step1" class="step active">
            <div class="input-group">
                <label>请输入密钥</label>
                <input type="password" id="secretKey" placeholder="请输入密钥">
            </div>
            <div class="qr-info">
                请添加wx好友获取密钥:<br>
                wx号码:writhing1741914128<br>
            </div>
            <button onclick="verifyKey()">验证密钥</button>
            <div id="keyError" class="error"></div>
        </div>

        <!-- Token上传步骤 -->
        <div id="step2" class="step">
            <div class="input-group">
                <label>请输入Token</label>
                <input type="text" id="token" placeholder="请输入至少32位的Token">
            </div>
            <button onclick="uploadToken()">上传Token</button>
            <div id="tokenError" class="error"></div>
            <div id="tokenSuccess" class="success"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // 移除静态密钥定义
        let CORRECT_KEY = ''; // 将从接口获取

        // 添加获取密钥的函数
        async function fetchSecretKey() {
            try {
                const response = await fetch('http://localhost/token/getKeyForIdOne');
                const data = await response.json();
                
                if (data.code === 200) {
                    CORRECT_KEY = data.data;
                } else {
                    throw new Error(data.message || '获取密钥失败');
                }
            } catch (error) {
                console.error('获取密钥失败:', error);
                // 显示错误提示
                const keyError = document.getElementById('keyError');
                keyError.textContent = '系统错误，请稍后重试';
                keyError.style.display = 'block';
            }
        }

        // 修改验证密钥函数
        async function verifyKey() {
            const secretKey = document.getElementById('secretKey').value;
            const keyError = document.getElementById('keyError');

            try {
                // 确保已获取最新密钥
                await fetchSecretKey();

                if (secretKey === CORRECT_KEY) {
                    // 添加成功提示
                    await Swal.fire({
                        title: '验证成功！',
                        text: '即将进入Token上传页面',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false,
                        background: '#fff',
                        iconColor: '#4CAF50',
                        customClass: {
                            popup: 'animated fadeInDown'
                        }
                    });
                    
                    document.getElementById('step1').classList.remove('active');
                    document.getElementById('step2').classList.add('active');
                    keyError.style.display = 'none';
                } else {
                    // 添加错误提示
                    Swal.fire({
                        title: '验证失败',
                        text: '密钥错误，请重试',
                        icon: 'error',
                        confirmButtonText: '确定',
                        confirmButtonColor: '#1a73e8'
                    });
                    keyError.style.display = 'none';
                }
            } catch (error) {
                Swal.fire({
                    title: '系统错误',
                    text: '验证失败，请稍后重试',
                    icon: 'warning',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#1a73e8'
                });
                keyError.style.display = 'none';
            }
        }

        // 页面加载时获取密钥
        document.addEventListener('DOMContentLoaded', fetchSecretKey);

        async function uploadToken() {
            const token = document.getElementById('token').value;
            const tokenError = document.getElementById('tokenError');
            const tokenSuccess = document.getElementById('tokenSuccess');

            // 验证token长度
            if (token.length < 32) {
                tokenError.textContent = 'Token长度必须至少为32位';
                tokenError.style.display = 'block';
                tokenSuccess.style.display = 'none';
                return;
            }

            try {
                const response = await fetch('http://localhost/token/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        token: token,
                        key: "123"  // 添加 key 参数
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    await Swal.fire({
                        title: '上传成功！',
                        text: '每日十点即可获得对应积分',
                        icon: 'success',
                        confirmButtonText: '好的',
                        confirmButtonColor: '#4CAF50'
                    });
                    document.getElementById('token').value = '';
                    tokenError.style.display = 'none';
                    tokenSuccess.style.display = 'none';
                } else {
                    throw new Error(data.message || '上传失败');
                }
            } catch (error) {
                Swal.fire({
                    title: '上传失败',
                    text: error.message || '请稍后重试',
                    icon: 'error',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#1a73e8'
                });
                tokenError.style.display = 'none';
                tokenSuccess.style.display = 'none';
            }
        }

        // 添加输入框回车键支持
        document.getElementById('secretKey').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') verifyKey();
        });

        document.getElementById('token').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') uploadToken();
        });
    </script>
</body>
</html>

<style>
/* 移动端优化 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 25px;
        width: 95%;
        max-width: none;
    }

    input, button {
        min-height: 48px; /* 更大的点击区域 */
        font-size: 16px; /* 防止iOS缩放 */
    }

    .title {
        font-size: 1.8em;
        margin-bottom: 20px;
    }

    .input-group {
        margin-bottom: 20px;
    }

    label {
        font-size: 1em;
        margin-bottom: 10px;
    }

    .qr-info {
        padding: 20px;
        margin: 20px 0;
        font-size: 15px;
    }

    .error, .success {
        padding: 15px;
        margin-top: 20px;
        font-size: 15px;
        border-radius: 12px;
    }
}

/* 添加触摸反馈效果 */
@media (hover: none) {
    button:active {
        transform: scale(0.98);
        opacity: 0.9;
    }

    input:focus {
        transform: translateY(-1px);
    }

    .container {
        transform: none !important;
    }
}

/* 优化滚动体验 */
body {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* 优化输入体验 */
input {
    -webkit-appearance: none;
    appearance: none;
    border-radius: 12px;
}

/* 优化按钮触摸区域 */
button {
    position: relative;
    touch-action: manipulation;
}

/* 添加触摸涟漪效果 */
button::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: radial-gradient(circle, white 10%, transparent 10.01%) no-repeat 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform .5s, opacity 1s;
}

button:active::after {
    transform: scale(0, 0);
    opacity: .3;
    transition: 0s;
}

/* 优化表单组件间距 */
.form-group + .form-group {
    margin-top: 20px;
}

/* 优化错误提示显示 */
.error, .success {
    max-width: 100%;
    word-break: break-word;
    animation: slideIn .3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
</style>
