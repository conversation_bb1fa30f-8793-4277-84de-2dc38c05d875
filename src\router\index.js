import { createRouter, createWebHistory } from 'vue-router';
import UserMannger from '../components/UserManager.vue';
import ShopMannger from '../components/ShopMannger.vue';
import Login from '../components/Login.vue';
import Index from '../views/Index.vue';
import OrderManager from '../components/OrderManager.vue';

const isAuthenticated = () => {
  return sessionStorage.getItem('isAuthenticated') === 'true';
};

const routes = [
  {
    path: '/',
    redirect: '/index',
  },
  {
    path: '/login',
    component: Login,
  },
  {
    path: '/index',
    component: Index,
    meta: { requiresAuth: true },
  },
  {
    path: '/user',
    component: UserMannger,
    meta: { requiresAuth: true },
  },
  {
    path: '/shop',
    component: ShopMannger,
    meta: { requiresAuth: true },
  },
  {
    path: '/orders',
    component: OrderManager,
    meta: { requiresAuth: true },
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login'); // 未登录时跳转到登录页面
  } else {
    next(); // 已登录或不需要权限时放行
  }
});

export default router;