import axios from 'axios';

const BASE_URL = 'http://localhost:8081/api'; // Adjust this based on your API base URL

// 创建一个 axios 实例
const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000, // 10 秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      data: config.data,
      headers: config.headers
    });
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    console.log('收到响应:', response);
    return response;
  },
  error => {
    console.error('响应错误:', error);
    if (error.code === 'ECONNABORTED') {
      return Promise.reject(new Error('请求超时，请稍后重试'));
    }
    if (!error.response) {
      return Promise.reject(new Error('网络错误，请检查网络连接'));
    }
    return Promise.reject(error);
  }
);

const orderService = {
  // 获取所有订单
  getAllOrders() {
    console.log('调用 getAllOrders');
    return instance.get('/orders/all').catch(error => {
      console.error('获取订单列表失败:', error);
      throw error;
    });
  },

  // 更新订单状态
  updateOrderStatus(order) {
    console.log('调用 updateOrderStatus:', order);
    // 构造符合后端要求的请求数据
    const updateData = {
      orderId: order.orderId,
      username: order.username,
      orderData: order.orderData, // 保持原始字符串格式
      createTime: order.createTime,
      status: order.status
    };

    return instance.put('/orders', updateData).catch(error => {
      console.error('更新订单状态失败:', error);
      throw error;
    });
  }
};

export default orderService; 